package com.swhd.oauth.api.oceanengine.client;

import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferCreateParam;
import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferQueryParam;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferCreateResult;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferQueryResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 巨量引擎转账API客户端
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = OceanengineTransferApiClient.BASE_PATH)
public interface OceanengineTransferApiClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/oceanengine/transfer/api";

    @Operation(summary = "发起账户转账")
    @PostMapping("/transferCreate")
    Rsp<OceanengineTransferCreateResult> transferCreate(@RequestBody @Valid OceanengineTransferCreateParam param);

    @Operation(summary = "查询账户转账")
    @PostMapping("/transferQuery")
    Rsp<OceanengineTransferQueryResult> transferQuery(@RequestBody @Valid OceanengineTransferQueryParam param);

}
