package com.swhd.oauth.api.oceanengine.dto.param.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询账户转账请求参数
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "查询账户转账请求参数")
public class OceanengineTransferQueryParam {

    @Schema(description = "请求唯一编号，建议用uuid，防止重复转账", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请求唯一编号不能为空")
    private String bizRequestNo;

    @Schema(description = "代理商账户id，用于鉴权", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "代理商账户id不能为空")
    private Long agentId;

    @Schema(description = "发起转账的幂等id")
    private Long transferBizRequestNo;

    @Schema(description = "转账单号")
    private String transferSerial;
}
