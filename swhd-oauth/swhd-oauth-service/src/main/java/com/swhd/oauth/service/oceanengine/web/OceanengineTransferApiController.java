package com.swhd.oauth.service.oceanengine.web;

import com.swhd.oauth.api.oceanengine.client.OceanengineTransferApiClient;
import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferCreateParam;
import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferQueryParam;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferCreateResult;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferQueryResult;
import com.swhd.oauth.service.oceanengine.service.OceanengineTransferApiService;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 巨量引擎转账API控制器
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(OceanengineTransferApiClient.BASE_PATH)
public class OceanengineTransferApiController implements OceanengineTransferApiClient {

    private final OceanengineTransferApiService oceanengineTransferApiService;

    @Override
    public Rsp<OceanengineTransferCreateResult> transferCreate(OceanengineTransferCreateParam param) {
        return oceanengineTransferApiService.transferCreate(param);
    }

    @Override
    public Rsp<OceanengineTransferQueryResult> transferQuery(OceanengineTransferQueryParam param) {
        return oceanengineTransferApiService.transferQuery(param);
    }
}
